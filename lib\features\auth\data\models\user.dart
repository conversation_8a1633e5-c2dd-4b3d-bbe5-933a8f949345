import 'package:bond_network/bond_network.dart';
import 'package:json_annotation/json_annotation.dart';

part 'user.g.dart';

part '../enums/user_status.dart';

@JsonSerializable(explicitToJson: true)
class User extends Model {
  final String? name;
  final String? email;
  final String phone;
  @J<PERSON><PERSON><PERSON>(name: 'country_code')
  final String countryCode;
  final String? image;
  final UserStatus? status;
  @<PERSON>sonKey(name: 'total_point', defaultValue: 0)
  final int totalPoint;
  @<PERSON>son<PERSON>ey(name: 'hold_point', defaultValue: 0)
  final int holdPoint;
  @<PERSON>son<PERSON>ey(name: 'available_point', defaultValue: 0)
  final int availablePoint;
  @<PERSON>son<PERSON><PERSON>(name: 'last_login_at')
  final String? lastLoginAt;
  @Json<PERSON><PERSON>(name: 'email_verified_at')
  final String? emailVerifiedAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'nick_name')
  final String? nickName;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'avatar_id')
  final int? avatarId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'has_address', defaultValue: false)
  final bool? hasAddress;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'show_renewal_notification', defaultValue: false)
  final bool showRenewalNotification;

  const User({
    required super.id,
    required this.phone,
    required this.countryCode,
    required this.totalPoint,
    required this.holdPoint,
    required this.availablePoint,
    required this.showRenewalNotification,
    this.status,
    this.lastLoginAt,
    this.emailVerifiedAt,
    this.nickName,
    this.email,
    this.name,
    this.image,
    this.avatarId,
    this.hasAddress,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$UserToJson(this);

  @override
  List<Object?> get props => [id, countryCode, phone];

  bool get isCompleteProfile =>
      image != null && nickName != null && email != null && name != null;

  String get fullPhone => "$countryCode$phone";
}
