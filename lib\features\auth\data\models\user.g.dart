// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

User _$UserFromJson(Map<String, dynamic> json) => User(
      id:  int.tryParse("${json['id']}")?? 0 ,
      phone: json['phone'] as String,
      countryCode: json['country_code'] as String,
      totalPoint: (json['total_point'] as num?)?.toInt() ?? 0,
      holdPoint: (json['hold_point'] as num?)?.toInt() ?? 0,
      availablePoint: (json['available_point'] as num?)?.toInt() ?? 0,
      showRenewalNotification: json['show_renewal_notification'] as bool? ?? false,
      status: $enumDecodeNullable(_$UserStatusEnumMap, json['status']),
      lastLoginAt: json['last_login_at'] as String?,
      emailVerifiedAt: json['email_verified_at'] as String?,
      nickName: json['nick_name'] as String?,
      email: json['email'] as String?,
      name: json['name'] as String?,
      image: json['image'] as String?,
      avatarId: (json['avatar_id'] as num?)?.toInt(),
      hasAddress: json['has_address'] as bool? ?? false,
    );

Map<String, dynamic> _$UserToJson(User instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
      'phone': instance.phone,
      'country_code': instance.countryCode,
      'image': instance.image,
      'status': _$UserStatusEnumMap[instance.status],
      'total_point': instance.totalPoint,
      'hold_point': instance.holdPoint,
      'available_point': instance.availablePoint,
      'show_renewal_notification': instance.showRenewalNotification,
      'last_login_at': instance.lastLoginAt,
      'email_verified_at': instance.emailVerifiedAt,
      'nick_name': instance.nickName,
      'avatar_id': instance.avatarId,
      'has_address': instance.hasAddress,
    };

const _$UserStatusEnumMap = {
  UserStatus.active: 'active',
  UserStatus.frozen: 'frozen',
  UserStatus.inActive: 'in-active',
};
