<?php

namespace App\Http\Controllers\Auth;

use App\Enums\UserType;
use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\AnonymousLoginRequest;
use App\Http\Resources\UserResource;
use App\Models\DeviceInfo;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class AnonymousUserController extends Controller
{
    /**
     * Handle anonymous login request.
     *
     * @param AnonymousLoginRequest $request
     * @return UserResource
     */
    public function __invoke(AnonymousLoginRequest $request): UserResource
    {
        // Find existing device info by device_id
        $deviceInfo = DeviceInfo::query()->where('device_id', $request->device_id)->first();

        if ($deviceInfo) {
            // If device exists, get the associated user and update first login status
            $user = $deviceInfo->user;
            $user->is_first_login = false;
            $user->save();
        } else {
            // Create a new anonymous user
            $user = User::query()->create([
                'name' => 'Guest User ' . random_int(10000, 99999),
                'email' => 'guest_' . Str::random(10) . '@example.com',
                'password' => Hash::make(Str::random(16)),
                'user_type' => UserType::User,
                'is_anonymous' => true,
                'is_first_login' => true,
            ]);
        }

        // Update or create device info for the user
        $user->deviceInfo()->updateOrCreate(
            ['device_id' => $request->device_id],
            $request->validated()
        );

        // Return the user resource with token
        return UserResource::make($user)
            ->withToken('anonymous-token');
    }
}
