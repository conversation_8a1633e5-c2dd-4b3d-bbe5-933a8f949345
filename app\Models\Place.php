<?php

namespace App\Models;

use App\Enums\PlacePersona;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Place extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    // unguarded
    protected $guarded = [];

    protected $casts = [
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'rating' => 'decimal:2',
        'opening_hours' => 'array',
        'name' => 'array',
        'description' => 'array',
        'address' => 'array',
        'photos' => 'array',
        'persona' => PlacePersona::class,
        'is_active' => 'boolean',
        'is_choice' => 'boolean',
        'is_trending' => 'boolean',
        'family_friendly' => 'boolean',
        'wheelchair_accessible' => 'boolean',
        'pet_friendly' => 'boolean',
    ];

    protected $with = ['media'];

    public function recommendations(): HasMany
    {
        return $this->hasMany(Recommendation::class);
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }



    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('place_photos');
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeChoice($query)
    {
        return $query->where('is_choice', true);
    }

    public function scopeTrend($query)
    {
        return $query->where('is_trending', true);

    }

    public function agent(): BelongsTo
    {
        return $this->belongsTo(Agent::class);
    }

    /**
     * Get the favorites for the place.
     */
    public function favorites(): HasMany
    {
        return $this->hasMany(PlaceFavorite::class);
    }

    public function images(): Attribute
    {
        return Attribute::get(function () {
            $media = $this->getMedia('place_photos');
            $imagesLinks = [];

            foreach ($media as $file) {
                $imagesLinks[] = $file->getFullUrl(); // Push string, not array
            }

            return $imagesLinks;
        });
    }

}
